<template>
  <j-modal
    :title="title"
    :width="width"
    v-model:open="open"
    :footer="null"
    @cancel="handleCancel"
    class="tech-modal"
    :style="{ zIndex: getModalZIndex() }"
    :get-container="getModalContainer"
    :mask-closable="true"
    :keyboard="true"
    :centered="true"
  >
    <div class="tech-container mx-auto px-4 py-6 relative">
      <!-- 计划信息卡片 -->
      <div class="tech-card bg-gradient-to-br from-white/98 to-blue-50/95 backdrop-blur-xl rounded-2xl shadow-tech p-6 mb-6 transform transition-all duration-500 hover:shadow-tech-hover relative border border-blue-200/40">
        <!-- 科技感装饰元素 -->
        <div class="absolute top-0 right-0 w-40 h-1 bg-gradient-to-r from-transparent via-blue-400 to-cyan-500 animate-pulse-slow"></div>
        <div class="absolute bottom-0 left-0 w-40 h-1 bg-gradient-to-r from-cyan-500 via-blue-400 to-transparent animate-pulse-slow"></div>
        
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center shadow-lg">
              <Icon icon="mdi:clipboard-text" class="text-white text-2xl" />
            </div>
            <div>
              <h2 class="text-2xl font-bold text-gray-800 mb-1">{{ info.name || '民警巡更计划' }}</h2>
              <p class="text-gray-600">计划详情信息</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <div class="px-3 py-1 rounded-full text-sm font-medium" :class="getStatusClass(info.status)">
              {{ getStatusText(info.status) }}
            </div>
          </div>
        </div>

        <!-- 基本信息网格 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
          <div class="info-item">
            <div class="flex items-center mb-2">
              <Icon icon="mdi:location" class="text-blue-500 mr-2" size="20" />
              <span class="text-sm font-medium text-gray-600">路线名称</span>
            </div>
            <div class="text-lg font-semibold text-gray-800">{{ info.lineName || '未设置' }}</div>
          </div>

          <div class="info-item">
            <div class="flex items-center mb-2">
              <Icon icon="mdi:clock-start" class="text-green-500 mr-2" size="20" />
              <span class="text-sm font-medium text-gray-600">开始时间</span>
            </div>
            <div class="text-lg font-semibold text-gray-800">{{ info.startTime || '未设置' }}</div>
          </div>

          <div class="info-item">
            <div class="flex items-center mb-2">
              <Icon icon="mdi:clock-end" class="text-red-500 mr-2" size="20" />
              <span class="text-sm font-medium text-gray-600">结束时间</span>
            </div>
            <div class="text-lg font-semibold text-gray-800">{{ info.endTime || '未设置' }}</div>
          </div>

          <div class="info-item">
            <div class="flex items-center mb-2">
              <Icon icon="mdi:account-badge" class="text-purple-500 mr-2" size="20" />
              <span class="text-sm font-medium text-gray-600">巡更民警</span>
            </div>
            <div class="flex items-center space-x-2">
              <div v-if="getPatrolOfficerAvatar()" class="w-8 h-8 rounded-full overflow-hidden border-2 border-gray-200">
                <img :src="getPatrolOfficerAvatar()" :alt="getPatrolOfficerName()" class="w-full h-full object-cover" />
              </div>
              <div v-else class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
                <span class="text-sm text-gray-600">{{ (getPatrolOfficerName() || '未分配').charAt(0) }}</span>
              </div>
              <span class="text-lg font-semibold text-gray-800">{{ getPatrolOfficerName() || '未分配' }}</span>
            </div>
          </div>

          <div class="info-item" v-if="info.patrolDuration || info.duration">
            <div class="flex items-center mb-2">
              <Icon icon="mdi:timer" class="text-cyan-500 mr-2" size="20" />
              <span class="text-sm font-medium text-gray-600">巡更时长</span>
            </div>
            <div class="text-lg font-semibold text-gray-800">{{ info.patrolDuration || info.duration || '未设置' }}</div>
          </div>

          <div class="info-item">
            <div class="flex items-center mb-2">
              <Icon icon="mdi:calendar" class="text-orange-500 mr-2" size="20" />
              <span class="text-sm font-medium text-gray-600">创建时间</span>
            </div>
            <div class="text-lg font-semibold text-gray-800">{{ info.createTime || '未知' }}</div>
          </div>

        </div>

        <!-- 巡更统计信息 -->
        <div v-if="info.patrolStatistics" class="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-4 mb-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <Icon icon="mdi:chart-bar" class="text-blue-500 mr-2" size="20" />
            巡更统计
          </h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600">{{ info.patrolStatistics.totalPoints || 0 }}</div>
              <div class="text-sm text-gray-600">巡更点数</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600">{{ info.patrolStatistics.normalCount || 0 }}</div>
              <div class="text-sm text-gray-600">正常数量</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-orange-600">{{ info.patrolStatistics.pendingCount || 0 }}</div>
              <div class="text-sm text-gray-600">待巡数量</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-red-600">{{ info.patrolStatistics.missedCount || 0 }}</div>
              <div class="text-sm text-gray-600">漏巡数量</div>
            </div>
          </div>
        </div>

        <!-- 备注信息 -->
        <div v-if="info.remark" class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl p-4 mb-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-2 flex items-center">
            <Icon icon="mdi:note-text" class="text-orange-500 mr-2" size="20" />
            备注信息
          </h3>
          <p class="text-gray-700 leading-relaxed">{{ info.remark }}</p>
        </div>

        <!-- 巡更点列表 -->
        <div v-if="getPatrolPointsList().length > 0" class="bg-white rounded-xl shadow-sm border border-gray-200">
          <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
              <Icon icon="mdi:map-marker-multiple" class="text-blue-500 mr-2" size="20" />
              巡更点列表
              <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                {{ getPatrolPointsList().length }} 个点位
              </span>
            </h3>
          </div>

          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">巡更点</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">分组</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">第几次巡更</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">打卡次数</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">巡更时间</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">巡更截图</th>
                  <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">视频监控</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="(record, index) in getPatrolPointsList()" :key="record.id || index" class="hover:bg-gray-50 transition-colors">
                  <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-600">{{ index + 1 }}</td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center justify-center">
                      <div class="w-8 h-8 rounded-full bg-gradient-to-br from-blue-100 to-cyan-100 flex items-center justify-center mr-3 border border-blue-200">
                        <Icon icon="mdi:location" class="text-blue-600" size="16" />
                      </div>
                      <div class="text-sm font-medium text-gray-800">{{ record.cardName || record.name || '未命名' }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-600">
                    {{ record.departName || record.sectionName || record.groupName || '-' }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-600">{{ record.numberOfTimes || '-' }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-600">{{ record.num || 0 }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-600">{{ record.patrolTime || record.checkTime || '未巡更' }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-center">
                    <span class="px-2 py-1 text-xs font-medium rounded-full" :class="getPatrolStatusClass(record.status)">
                      {{ getPatrolStatusText(record.status) }}
                    </span>
                  </td>
                  <td class="px-3 py-4 whitespace-nowrap text-center">
                    <div class="flex items-center justify-center">
                      <a-button
                        v-if="record.screenshots && record.screenshots.length > 0"
                        type="primary"
                        size="small"
                        @click="viewScreenshots(record)"
                        class="tech-button"
                        title="查看巡更截图"
                      >
                        <Icon icon="mdi:camera" size="14" />
                        <span class="hidden sm:inline ml-1">截图({{ record.screenshots.length }})</span>
                        <span class="sm:hidden ml-1">{{ record.screenshots.length }}</span>
                      </a-button>
                      <span v-else class="text-gray-400 text-xs">无截图</span>
                    </div>
                  </td>
                  <td class="px-3 py-4 whitespace-nowrap text-center">
                    <div class="flex items-center justify-center">
                      <a-button
                        v-if="record.videoUrl"
                        type="primary"
                        size="small"
                        @click="playVideo(record)"
                        class="tech-button"
                        title="播放视频监控"
                      >
                        <Icon icon="mdi:video" size="14" />
                        <span class="hidden sm:inline ml-1">视频监控</span>
                      </a-button>
                      <span v-else class="text-gray-400 text-xs">无视频</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 无巡更点提示 -->
        <div v-else class="bg-gray-50 rounded-xl p-8 text-center">
          <Icon icon="mdi:map-marker-off" class="text-gray-400 text-4xl mb-4" />
          <p class="text-gray-500">暂无巡更点信息</p>
        </div>
      </div>
    </div>

    <!-- 截图查看弹窗 -->
    <j-modal
      v-model:open="screenshotModalOpen"
      title="巡更截图"
      :width="800"
      :footer="null"
      class="tech-modal"
    >
      <div class="screenshot-gallery">
        <div v-if="currentScreenshots.length > 0" class="grid grid-cols-2 gap-4">
          <div
            v-for="(screenshot, index) in currentScreenshots"
            :key="index"
            class="screenshot-item"
          >
            <img
              :src="screenshot.url"
              :alt="`截图${index + 1}`"
              class="w-full h-48 object-cover rounded-lg border border-gray-200 cursor-pointer hover:shadow-lg transition-shadow"
              @click="previewImage(screenshot.url)"
            />
            <div class="mt-2 text-sm text-gray-600 text-center">
              {{ screenshot.time || `截图${index + 1}` }}
            </div>
          </div>
        </div>
        <div v-else class="text-center py-8">
          <Icon icon="mdi:camera-off" class="text-gray-400 text-4xl mb-4" />
          <p class="text-gray-500">暂无截图</p>
        </div>
      </div>
    </j-modal>

    <!-- 视频监控播放弹窗 -->
    <j-modal
      v-model:open="videoModalOpen"
      :title="`视频监控 - ${currentVideoRecord?.cardName || '巡更点'}`"
      :width="1000"
      :footer="null"
      class="tech-modal"
      @cancel="handleVideoModalClose"
    >
      <div class="video-monitor-container">
        <VideoMonitorPlayerModal
          v-if="videoModalOpen && currentVideoInfo"
          ref="videoPlayerRef"
          :plan-id="info.id"
          :video-info="currentVideoInfo"
          :auto-start="true"
          :show-controls="true"
        />
      </div>
    </j-modal>
  </j-modal>
</template>

<script setup lang="ts">
import { ref, defineExpose, onMounted, onUnmounted } from 'vue';
import JModal from '@/components/Modal/src/JModal/JModal.vue';
import { Icon } from '/@/components/Icon';
import { getDetail } from '../PolicePlan.api';
import VideoMonitorPlayerModal from '@/views/plan/components/VideoMonitorPlayerModal.vue';
import { message } from 'ant-design-vue';
import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';

const width = ref<number>(1400); // 增加宽度以容纳新增的列
const title = ref<string>('民警巡更计划详情');
const open = ref<boolean>(false);
const info = ref<any>({});
const emit = defineEmits(['register', 'success']);

// 全屏检测
const isInFullscreen = ref<boolean>(false);

// 检测是否在全屏模式
const checkFullscreenStatus = () => {
  const fullscreenElement = document.fullscreenElement ||
                           (document as any).webkitFullscreenElement ||
                           (document as any).mozFullScreenElement ||
                           (document as any).msFullscreenElement;
  isInFullscreen.value = !!fullscreenElement;
  console.log('检测全屏状态:', isInFullscreen.value, fullscreenElement);
};

// 获取弹窗的z-index
const getModalZIndex = () => {
  // 实时检测全屏状态
  checkFullscreenStatus();
  // 在全屏模式下使用更高的z-index，非全屏模式下也使用较高的z-index确保显示
  return isInFullscreen.value ? 100000 : 9999;
};

// 获取弹窗容器
const getModalContainer = () => {
  // 总是返回 document.body 确保弹窗能正确显示
  return document.body;
};

// 截图相关
const screenshotModalOpen = ref<boolean>(false);
const currentScreenshots = ref<any[]>([]);

// 视频相关
const videoModalOpen = ref<boolean>(false);
const currentVideoRecord = ref<any>(null);
const currentVideoInfo = ref<any>(null);
const videoPlayerRef = ref<any>(null);

// 显示弹窗
function showModal(record: any) {
  console.log('PolicePlanDetailModal showModal 被调用:', record);

  // 检测全屏状态
  checkFullscreenStatus();
  console.log('全屏状态:', isInFullscreen.value);

  // 先设置基本信息，确保弹窗能显示
  info.value = {
    id: record.id || Date.now(),
    name: record.name || '巡更计划',
    lineName: record.lineName || record.routeName || '未知路线',
    startTime: record.startTime || '未设置',
    endTime: record.endTime || '未设置',
    status: record.status || 0,
    patrolUserName: record.patrolUserName || record.patrolOfficer?.name || '未知',
    patrolUserAvatar: record.patrolUserAvatar || record.patrolOfficer?.avatar || '',
    checkpoints: record.checkpoints || [],
    planCardList: record.planCardList || []
  };

  // 使用 nextTick 确保DOM更新后再显示弹窗
  setTimeout(() => {
    open.value = true;
    title.value = `民警巡更计划详情 - ${record.name || ''}`;
    console.log('弹窗状态设置为打开:', open.value);
  }, 50);

  // 如果传入的record已经包含checkpoints或planCardList，说明是详细数据，直接使用
  if (record.checkpoints || record.planCardList) {
    console.log('使用传入的详细数据');
    // 为巡更点添加模拟的截图和视频数据
    processPatrolPointsData();
  } else if (record.id) {
    // 否则调用API获取详情
    console.log('调用API获取详情，ID:', record.id);
    detail(record.id);
  } else {
    console.log('没有ID，使用基本数据显示');
  }
}

// 获取详情数据
function detail(id: string | number) {
  if (!id) {
    console.error('缺少计划ID，无法获取详情');
    message.error('缺少计划ID，无法获取详情');
    return;
  }

  getDetail({ id: id })
    .then((res) => {
      if (res.success) {
        info.value = res.result;
        // 为巡更点添加模拟的截图和视频数据
        processPatrolPointsData();
      } else {
        console.error('获取详情失败:', res.message);
        message.error(res.message || '获取详情失败');
      }
    })
    .catch((error) => {
      console.error('获取详情失败:', error);
      message.error('网络错误，获取详情失败');
    });
}

// 处理巡更点数据，添加模拟的截图和视频
function processPatrolPointsData() {
  // 处理 planCardList 结构
  if (info.value.planCardList && info.value.planCardList.length > 0) {
    info.value.planCardList = info.value.planCardList.map((card: any, index: number) => ({
      ...card,
      // 模拟截图数据 - 为偶数索引的巡更点添加截图
      screenshots: card.screenshots || (index % 2 === 0 ? [
        {
          url: `https://picsum.photos/400/300?random=${index * 2 + 1}`,
          time: new Date(Date.now() - Math.random() * 3600000).toLocaleString()
        },
        {
          url: `https://picsum.photos/400/300?random=${index * 2 + 2}`,
          time: new Date(Date.now() - Math.random() * 1800000).toLocaleString()
        }
      ] : []),
      // 模拟视频数据 - 为每3个巡更点中的第1个添加视频
      videoUrl: card.videoUrl || (index % 3 === 0 ? `rtsp://demo.stream/camera${index + 1}` : null),
      rtspUrl: card.rtspUrl || (index % 3 === 0 ? `rtsp://demo.stream/camera${index + 1}` : null),
      streamId: card.streamId || `stream_${card.id || index}`,
      streamType: card.streamType || 'rtsp',
      websocketUrl: card.websocketUrl || (index % 3 === 0 ? `ws://demo.stream/ws/camera${index + 1}` : null)
    }));
  }
  
  // 处理 checkpoints 结构
  if (info.value.checkpoints && info.value.checkpoints.length > 0) {
    info.value.checkpoints.forEach((checkpoint: any) => {
      if (checkpoint.planCards && checkpoint.planCards.length > 0) {
        checkpoint.planCards = checkpoint.planCards.map((card: any, cardIndex: number) => ({
          ...card,
          screenshots: card.screenshots || (cardIndex % 2 === 0 ? [
            {
              url: `https://picsum.photos/400/300?random=${cardIndex * 2 + 1}`,
              time: new Date(Date.now() - Math.random() * 3600000).toLocaleString()
            }
          ] : []),
          videoUrl: card.videoUrl || (cardIndex % 3 === 0 ? `rtsp://demo.stream/camera${cardIndex + 1}` : null),
          rtspUrl: card.rtspUrl || (cardIndex % 3 === 0 ? `rtsp://demo.stream/camera${cardIndex + 1}` : null),
          streamId: card.streamId || `stream_${card.id || cardIndex}`,
          streamType: card.streamType || 'rtsp',
          websocketUrl: card.websocketUrl || (cardIndex % 3 === 0 ? `ws://demo.stream/ws/camera${cardIndex + 1}` : null)
        }));
      }
    });
  }
}

// 获取状态样式类
function getStatusClass(status: number) {
  switch (parseInt(status)) {
    case 0:
      return 'bg-orange-100 text-orange-800 border border-orange-200';
    case 1:
      return 'bg-blue-100 text-blue-800 border border-blue-200';
    case 2:
      return 'bg-green-100 text-green-800 border border-green-200';
    default:
      return 'bg-gray-100 text-gray-800 border border-gray-200';
  }
}

// 获取状态文本
function getStatusText(status: number) {
  switch (parseInt(status)) {
    case 0:
      return '待巡';
    case 1:
      return '计划进行中';
    case 2:
      return '已完成';
    default:
      return '未知';
  }
}

// 获取巡更点状态样式类
function getPatrolStatusClass(status: number) {
  switch (parseInt(status)) {
    case 0:
      return 'bg-orange-100 text-orange-800';
    case 1:
      return 'bg-green-100 text-green-800';
    case 2:
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

// 获取巡更点状态文本
function getPatrolStatusText(status: number) {
  switch (parseInt(status)) {
    case 0:
      return '待巡';
    case 1:
      return '正常';
    case 2:
      return '漏检';
    default:
      return '未知';
  }
}

// 获取巡更民警头像
function getPatrolOfficerAvatar() {
  // 兼容多种数据结构
  return info.value.patrolUserAvatar || 
         info.value.patrolOfficer?.avatar || 
         (info.value.patrolOfficer && getFileAccessHttpUrl(info.value.patrolOfficer.avatar)) ||
         null;
}

// 获取巡更民警名称
function getPatrolOfficerName() {
  // 兼容多种数据结构
  return info.value.patrolUserName || 
         info.value.patrolOfficer?.name || 
         '未分配';
}

// 获取巡更点列表
function getPatrolPointsList() {
  // 优先使用 planCardList（原有结构）
  if (info.value.planCardList && info.value.planCardList.length > 0) {
    return info.value.planCardList;
  }
  
  // 兼容 checkpoints 结构（来自巡更记录大屏）
  if (info.value.checkpoints && info.value.checkpoints.length > 0) {
    const allCards: any[] = [];
    info.value.checkpoints.forEach((checkpoint: any) => {
      if (checkpoint.planCards && checkpoint.planCards.length > 0) {
        // 为每个卡片添加所属分组信息
        checkpoint.planCards.forEach((card: any) => {
          allCards.push({
            ...card,
            groupName: checkpoint.departName || checkpoint.sectionName || checkpoint.name
          });
        });
      } else {
        // 如果没有 planCards，则将 checkpoint 本身作为巡更点
        allCards.push({
          ...checkpoint,
          groupName: checkpoint.departName || checkpoint.sectionName || checkpoint.name
        });
      }
    });
    return allCards;
  }
  
  return [];
}

// 查看截图
function viewScreenshots(record: any) {
  if (!record.screenshots || record.screenshots.length === 0) {
    message.warning('该巡更点暂无截图数据');
    return;
  }

  currentScreenshots.value = record.screenshots;
  screenshotModalOpen.value = true;
}

// 预览图片
function previewImage(imageUrl: string) {
  // 使用浏览器原生方式预览图片
  window.open(imageUrl, '_blank');
}

// 播放视频
function playVideo(record: any) {
  if (!record.videoUrl && !record.rtspUrl) {
    message.warning('该巡更点暂无视频监控数据');
    return;
  }

  currentVideoRecord.value = record;

  // 构造视频信息对象，确保符合VideoMonitorPlayerModal的接口要求
  currentVideoInfo.value = {
    id: String(record.id || record.cardId || Date.now()),
    name: record.cardName || '巡更点视频',
    videoUrl: record.videoUrl || record.rtspUrl || '',
    streamId: record.streamId || `stream_${record.id || Date.now()}`,
    websocketUrl: record.websocketUrl || '',
    rtspUrl: record.rtspUrl || record.videoUrl || '',
    rtspVideo: record.rtspVideo || record.videoUrl || '',
    hlsUrl: record.hlsUrl || '',
    streamType: record.streamType || 'rtsp'
  };

  console.log('播放视频信息:', currentVideoInfo.value);
  videoModalOpen.value = true;
}

// 关闭视频弹窗
function handleVideoModalClose() {
  // 停止视频播放
  if (videoPlayerRef.value && videoPlayerRef.value.stopVideo) {
    videoPlayerRef.value.stopVideo();
  }

  videoModalOpen.value = false;
  currentVideoRecord.value = null;
  currentVideoInfo.value = null;
}

// 取消按钮回调
function handleCancel() {
  // 关闭所有弹窗
  screenshotModalOpen.value = false;
  handleVideoModalClose();
  open.value = false;
}

// 测试方法
function testModal() {
  console.log('testModal 被调用');
  open.value = true;
  title.value = '测试弹窗';
  info.value = {
    id: 'test',
    name: '测试计划',
    lineName: '测试路线',
    startTime: '09:00',
    endTime: '17:00',
    status: 1,
    patrolUserName: '测试用户',
    checkpoints: [],
    planCardList: []
  };
}

// 监听全屏状态变化
onMounted(() => {
  // 添加全屏状态变化监听器
  const handleFullscreenChange = () => {
    checkFullscreenStatus();
  };

  document.addEventListener('fullscreenchange', handleFullscreenChange);
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.addEventListener('mozfullscreenchange', handleFullscreenChange);
  document.addEventListener('MSFullscreenChange', handleFullscreenChange);

  // 初始检测
  checkFullscreenStatus();
});

onUnmounted(() => {
  // 清理全屏状态变化监听器
  const handleFullscreenChange = () => {
    checkFullscreenStatus();
  };

  document.removeEventListener('fullscreenchange', handleFullscreenChange);
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
  document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
});

defineExpose({
  showModal,
  detail,
  testModal
});
</script>

<style scoped lang="less">
.tech-modal {
  // 确保弹窗能正确显示，使用动态z-index
  position: relative;

  :deep(.ant-modal-root) {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: inherit !important;
  }

  :deep(.ant-modal-mask) {
    position: fixed !important;
    background: rgba(0, 0, 0, 0.8) !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: inherit !important;
  }

  :deep(.ant-modal-wrap) {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: inherit !important;
  }

  :deep(.ant-modal-content) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    overflow: hidden;
    position: relative !important;
    transform: none !important;
    margin: 0 !important;
    max-width: 95vw !important;
    max-height: 95vh !important;
    z-index: inherit !important;
  }

  :deep(.ant-modal-header) {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px 16px 0 0;
    position: relative !important;
  }

  :deep(.ant-modal-title) {
    color: white;
    font-weight: 600;
    position: relative !important;
  }

  :deep(.ant-modal-close) {
    color: white;
    position: relative !important;

    &:hover {
      background: rgba(255, 255, 255, 0.1) !important;
    }
  }

  :deep(.ant-modal-body) {
    padding: 0;
    background: #f8fafc;
    position: relative !important;
    max-height: 85vh !important;
    overflow-y: auto !important;
  }
}

.tech-container {
  min-height: 400px;
  max-height: 80vh;
  overflow-y: auto;
}

.tech-card {
  transition: all 0.3s ease;
}

.shadow-tech {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-tech-hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.info-item {
  padding: 16px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  border: 1px solid rgba(229, 231, 235, 0.5);
  transition: all 0.3s ease;
}

.info-item:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 自定义滚动条 */
.tech-container::-webkit-scrollbar {
  width: 6px;
}

.tech-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.tech-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.tech-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 科技感按钮样式 */
.tech-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tech-button:hover {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.tech-button:active {
  transform: translateY(0);
}

/* 截图画廊样式 */
.screenshot-gallery {
  padding: 16px;
}

.screenshot-item {
  transition: all 0.3s ease;
}

.screenshot-item:hover {
  transform: translateY(-2px);
}

/* 视频监控容器样式 */
.video-monitor-container {
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}
</style>
