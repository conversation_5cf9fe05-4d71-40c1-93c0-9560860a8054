<template>
  <div class="police-patrol-dashboard" :class="{ 'fullscreen-mode': isFullscreen }">
    <!-- 标题栏 -->
    <div class="dashboard-header">
      <div class="header-title">
        <div class="title-glow">{{ dashboardData.title }}</div>
      </div>
      <div class="header-actions">
        <div class="header-time">{{ currentTime }}</div>
        
        <a-button
          v-if="!isFullscreen"
          type="primary"
          class="fullscreen-btn"
          @click="toggleFullscreen"
          :icon="h(FullscreenOutlined)"
        >
          全屏显示
        </a-button>
        <a-button
          v-else
          type="default"
          class="exit-fullscreen-btn"
          @click="exitFullscreen"
          :icon="h(FullscreenExitOutlined)"
        >
          退出全屏
        </a-button>
        <a-button
          type="default"
          class="test-btn"
          @click="testModal"
          style="margin-left: 10px;"
        >
          测试弹窗
        </a-button>
      </div>
    </div>

    <!-- 全局查询条件 -->
    <div class="global-query-section">
      <div class="query-controls">
        <a-button
          :type="queryType === 'yesterday' ? 'primary' : 'default'"
          class="query-btn"
          @click="setQueryType('yesterday')"
        >
          <Icon icon="ant-design:calendar-outlined" />
          昨天
        </a-button>
        <a-button
          :type="queryType === 'today' ? 'primary' : 'default'"
          class="query-btn"
          @click="setQueryType('today')"
        >
          <Icon icon="ant-design:calendar-outlined" />
          今天
        </a-button>
        <a-date-picker
          v-model:value="customDate"
          class="date-picker"
          placeholder="选择日期"
          format="YYYY-MM-DD"
          @change="onCustomDateChange"
          @openChange="ensureDatePickerZIndex"
        />
      </div>
    </div>

    <!-- 民警信息区域 - 竖型显示 -->
    <div class="police-info-section">
      <!-- 民警数据加载状态 -->
      <div v-if="policeLoading" class="loading-police-info">
        <a-spin size="large" tip="正在加载民警信息...">
          <div class="loading-content">
            <Icon icon="ant-design:team-outlined" size="48" />
          </div>
        </a-spin>
      </div>

      <!-- 民警数据为空状态 -->
      <div v-else-if="dashboardData.policeList.length === 0" class="empty-police-info">

        <a-empty
          :image="Empty.PRESENTED_IMAGE_DEFAULT"
        
        >
         
          <template #description>
            <div class="empty-description">
              <div class="empty-title">暂无民警信息</div>
              <div class="empty-tips">
                <Icon icon="material-symbols:info-outline" />
                <span>请联系管理员配置民警信息或检查数据权限</span>
              </div>
             
            </div>
          </template>
        </a-empty>
      </div>

      <!-- 民警卡片列表 -->
      <transition-group v-else name="police-list" tag="div" class="police-cards">
        <div
          v-for="police in dashboardData.policeList"
          :key="police.id"
          class="police-card"
          :class="{ 'on-duty': police.status === 'on-duty' }"
        >
          <div class="police-avatar">
            <img
              v-if="police.avatar && !police.imageError"
              :src="getFileAccessHttpUrl(police.avatar)"
              :alt="police.name"
              @error="handleImageError($event, police)"
            />
            <div v-else class="avatar-placeholder" :style="{ backgroundColor: getAvatarColor(police.name) }">
              {{ getFirstLetter(police.name) }}
            </div>
            <div class="status-indicator" :class="police.status"></div>
          </div>
          <div class="police-info">
            <div class="police-name">{{ police.name }}</div>
            <div class="police-badge">{{ police.badge }}</div>
            <div class="police-department">{{ police.department }}</div>
          </div>
        </div>
      </transition-group>
    </div>

    <!-- 实时巡更计划状态 -->
    <div class="patrol-plans-section">
      <div class="section-title">
        <div class="title-line"></div>
        <span>巡更记录</span>
        <div class="title-line"></div>
      </div>



      <div class="plans-container">
        <div class="plans-scroll" ref="plansScrollRef">
          <!-- 查询加载状态 -->
          <div v-if="queryLoading" class="loading-patrol-records">
            <a-spin size="large" tip="正在查询巡更记录...">
              <div class="loading-content">
                <Icon icon="ant-design:search-outlined" size="48" />
              </div>
            </a-spin>
          </div>

          <!-- 空数据状态 -->
          <div v-else-if="sortedPlans.length === 0" class="empty-patrol-records">
            <a-empty
              :image="Empty.PRESENTED_IMAGE_DEFAULT"
              
            >
             
              <template #description>
                <div class="empty-description">
                  <div class="empty-title">暂无巡更记录</div>
                  
                  <div class="empty-tips">
                    <Icon icon="material-symbols:info-outline" />
                    <span>请尝试调整日期范围或等待巡更计划执行</span>
                  </div>
                 
                </div>
              </template>
            </a-empty>
          </div>

          <!-- 巡更记录列表 -->
          <div
            v-for="plan in sortedPlans"
            :key="plan.id"
            class="plan-item"
            :class="getPlanStatusClass(plan.status)"
            @click="viewPlanDetail(plan)"
            title="点击查看巡更计划详情"
          >
            <!-- 计划基本信息 - 一行显示 -->
            <div class="plan-summary">
              <div class="summary-item">
                <span class="label">计划:</span>
                <span class="value">{{ plan.name || '巡更计划' }}</span>
              </div>
              <div class="summary-item">
                <span class="label">巡更者:</span>
                <div class="staff-info">
                  <img :src="getFileAccessHttpUrl(plan.patrolOfficer?.avatar) || defaultAvatar" :alt="plan.patrolOfficer?.name" class="staff-avatar-tiny" />
                  <span class="value">{{ plan.patrolOfficer?.name || '未知' }}</span>
                </div>
              </div>
              <div class="summary-item">
                <span class="label">开始:</span>
                <span class="value">{{ plan.startTime }}</span>
              </div>
              <div class="summary-item" v-if="plan.endTime">
                <span class="label">结束:</span>
                <span class="value">{{ plan.endTime }}</span>
              </div>
              <div class="summary-item" v-if="plan.patrolDuration">
                <span class="label">用时:</span>
                <span class="value">{{ plan.patrolDuration}}</span>
              </div>
              <div class="plan-status-badge" :class="getPlanStatusClass(plan.status)">
                {{ getStatusText(plan.status) }}
              </div>
            </div>

            <!-- 巡更点状态 - 分组显示 -->
            
            <div class="patrol-points-groups" :class="{ 'in-progress-scan': isPlanInProgress(plan.status) }" v-if="plan.checkpoints">
           
              <div
                v-for="group in plan.checkpoints"
                :key="group.id"
                class="point-group"
                :class="getGroupStatusClass(group.planCards || [])"
              >
                <div class="group-header">
                  <span class="group-title">{{ group.departName || group.sectionName || group.cardName || group.name || '未知分组' }}</span>
                </div>
                <div class="group-points" v-if="group.planCards && group.planCards.length > 0">
                  <div
                    v-for="point in group.planCards"
                    :key="point.id"
                    class="patrol-point"
                    :class="[
                      `status-${getStatusClass(point.status)}`,
                      {
                        'is-next': isPlanInProgress(plan.status) && point.status === 0 && isNextPoint(plan, point)
                      }
                    ]"
                  >
                    <div class="point-icon" :class="`icon-${getStatusClass(point.status)}`">
                      <Icon :icon="getPointIcon(point.status)" />
                    </div>
                    <div class="point-info">
                      <div class="point-name">{{ point.cardName || point.name || '未知点位' }}</div>
                      <div class="point-time" >
                        <span>
                          {{ point.patrolTime || point.checkTime || "--:--"}}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 没有巡更卡片的提示 -->
                <div v-else class="no-plan-cards-tip">
                  <Icon icon="ant-design:info-circle-outlined" />
                  <span>该分组暂无巡更卡片</span>
                </div>
              </div>
            </div>

            <!-- 没有巡更点的提示 -->
            <div v-else class="no-checkpoints-tip">
              <Icon icon="ant-design:info-circle-outlined" />
              <span>该巡更计划暂无打卡点信息</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 巡更计划详情弹窗 -->
  <PolicePlanDetailModal ref="planDetailModalRef" />
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, h, computed, nextTick } from 'vue';
import { Empty, notification } from 'ant-design-vue';
import { FullscreenOutlined, FullscreenExitOutlined } from '@ant-design/icons-vue';
import { Icon } from '/@/components/Icon';
import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';

import dayjs from 'dayjs';
import {
  type PolicePatrolDashboardData,
  type PatrolPlan,
  getPolicePatrolData
} from './PolicePatrolDashboard.api';
import PolicePlanDetailModal from '../policePlan/components/PolicePlanDetailModal.vue';


// 响应式数据
const currentTime = ref('');
const isFullscreen = ref(false);
const dashboardData = ref<PolicePatrolDashboardData>({
  title: '监区民警巡更数据大屏',
  policeList: [],
  patrolPlans: [],
  statistics: {
    totalPlans: 0,
    completedPlans: 0,
    inProgressPlans: 0,
    missedPlans: 0,
    totalCheckpoints: 0,
    completedCheckpoints: 0,
    missedCheckpoints: 0
  }
});

// 巡更记录相关数据
const patrolPlans = ref<PatrolPlan[]>([]);
const plansScrollRef = ref();
const queryLoading = ref(false);
const policeLoading = ref(false);
const defaultAvatar = '/src/assets/images/ai/avatar.jpg';

// 查询相关数据
const queryType = ref<'yesterday' | 'today' | 'custom'>('yesterday');
const customDate = ref<any>(null);

// 详情弹窗相关
const detailModalVisible = ref(false);
const selectedPlan = ref<PatrolPlan | null>(null);

// 巡更计划详情弹窗ref
const planDetailModalRef = ref();

// 计算属性 - 排序后的计划
const sortedPlans = computed(() => {
  const plans = [...patrolPlans.value];
  return plans.sort((a, b) => {
    // 获取排序权重
    const getStatusOrder = (status: string | number) => {
      // 处理数字状态
      if (typeof status === 'number' || !isNaN(Number(status))) {
        const numStatus = Number(status);
        switch (numStatus) {
          case 1: return 0; // 进行中最优先
          case 0: return 1; // 待巡次之
          case 2: return 2; // 已完成最后
          default: return 3;
        }
      }
      
      // 处理字符串状态
      const statusOrder = { 'in-progress': 0, 'pending': 1, 'completed': 2, 'missed': 3 };
      return statusOrder[status] || 3;
    };
    
    return getStatusOrder(a.status) - getStatusOrder(b.status);
  });
});

// 获取状态文本
const getStatusText = (status: string | number) => {
  // 处理数字状态（巡更计划状态）
  if (typeof status === 'number' || !isNaN(Number(status))) {
    const numStatus = Number(status);
    switch (numStatus) {
      case 0: return '待巡';
      case 1: return '进行中';  
      case 2: return '已完成';
      default: return '未知状态';
    }
  }

  // 处理字符串状态（向后兼容）
  const statusMap = {
    'pending': '待开始',
    'in-progress': '进行中',
    'completed': '已完成',
    'missed': '已超时'
  };

  const statusStr = String(status).toLowerCase();
  return statusMap[statusStr] || statusStr;
};

// 获取状态对应的CSS类名（用于巡更点）
const getStatusClass = (status: string | number) => {
  if (typeof status === 'number' || !isNaN(Number(status))) {
    const numStatus = Number(status);
    switch (numStatus) {
      case 0: return 'pending';     // 待巡
      case 1: return 'checked';     // 正常
      case 2: return 'missed';      // 漏检
    }
  }

  // 支持字符串状态（向后兼容）
  const statusStr = String(status).toLowerCase();
  switch (statusStr) {
    case 'pending': return 'pending';
    case 'completed': return 'checked';
    case 'checked': return 'checked';
    case 'current': return 'current';
    case 'missed': return 'missed';
    default: return 'pending';
  }
};

// 获取计划状态对应的CSS类名
const getPlanStatusClass = (status: string | number) => {
  // 处理数字状态
  if (typeof status === 'number' || !isNaN(Number(status))) {
    const numStatus = Number(status);
    switch (numStatus) {
      case 0: return 'pending';       // 待巡
      case 1: return 'in-progress';   // 进行中
      case 2: return 'completed';     // 已完成
      default: return 'pending';
    }
  }

  // 处理字符串状态（向后兼容）
  const statusStr = String(status);
  switch (statusStr) {
    case 'pending': return 'pending';
    case 'in-progress': return 'in-progress';
    case 'completed': return 'completed';
    case 'missed': return 'missed';
    default: return 'pending';
  }
};

// 获取巡更点图标
const getPointIcon = (status: number | string) => {
  const iconMap = {
    0: 'ant-design:clock-circle-outlined',      // 待巡
    1: 'ant-design:check-circle-filled',        // 已巡更
    2: 'ant-design:close-circle-filled',        // 漏巡
    'pending': 'ant-design:clock-circle-outlined',
    'completed': 'ant-design:check-circle-filled',
    'checked': 'ant-design:check-circle-filled',
    'current': 'ant-design:environment-filled',
    'missed': 'ant-design:close-circle-filled'
  };
  
  // 处理字符串状态
  const key = typeof status === 'string' ? status.toLowerCase() : status;
  return iconMap[key] || 'ant-design:clock-circle-outlined';
};

// 获取当前巡更点索引
const getCurrentPointIndex = (plan: any) => {
  const checkpoints = plan.checkpoints || [];
  let lastCheckedIndex = -1;

  for (let i = 0; i < checkpoints.length; i++) {
    const checkpoint = checkpoints[i];
    const planCards = checkpoint.planCards || [];
    
    for (let j = 0; j < planCards.length; j++) {
      const card = planCards[j];
      if (card.status === 1) { // 1表示正常/已完成
        lastCheckedIndex = i * 100 + j; // 使用组合索引
      }
    }
  }

  return lastCheckedIndex;
};



// 获取分组状态样式类
const getGroupStatusClass = (planCards: any[]) => {
  if (!planCards || planCards.length === 0) return 'group-empty';

  const checkedCount = planCards.filter(p => 
    p.status === 'checked' || 
    p.status === 'completed' || 
    p.status === 1
  ).length;
  
  const missedCount = planCards.filter(p => 
    p.status === 'missed' || 
    p.status === 2
  ).length;
  
  const currentCount = planCards.filter(p => p.status === 'current').length;

  if (missedCount > 0) return 'group-has-missed';
  if (currentCount > 0) return 'group-in-progress';
  if (checkedCount === planCards.length) return 'group-completed';
  if (checkedCount > 0) return 'group-partial';
  return 'group-pending';
};



// 判断是否为下一个巡更点
const isNextPoint = (plan: any, point: any) => {
  // 简化逻辑：如果是待巡状态(0)，就可能是下一个点
  return point.status === 0;
};

// 辅助函数：判断计划状态
const isPlanInProgress = (status: string | number) => {
  return status === 'in-progress' || status === 1;
};

const isPlanCompleted = (status: string | number) => {
  return status === 'completed' || status === 2;
};

const isPlanPending = (status: string | number) => {
  return status === 'pending' || status === 0;
};



// 设置查询类型（用户交互时使用）
const setQueryType = (type: 'yesterday' | 'today' | 'custom', silent: boolean = false) => {
  queryType.value = type;

  // 如果选择昨天或今天，清空自定义日期选择框
  if (type === 'yesterday' || type === 'today') {
    customDate.value = null;
  }

  // 如果是静默模式，只设置类型不执行查询（用于初始化）
  if (silent) return;

  // 用户主动切换查询类型时，执行对应的查询
  if (type === 'yesterday') {
    const yesterday = dayjs().subtract(1, 'day');
    executeQueryByDate(yesterday.format('YYYY-MM-DD'), yesterday.format('YYYY-MM-DD'));
  } else if (type === 'today') {
    const today = dayjs();
    executeQueryByDate(today.format('YYYY-MM-DD'), today.format('YYYY-MM-DD'));
  }
  // custom类型需要用户选择日期后手动查询

  // 重启数据自动刷新，确保按新的查询条件刷新
  startDataRefresh();
};

// 自定义日期变化处理
const onCustomDateChange = (date: any) => {
  customDate.value = date;

  // 如果选择了日期，直接执行查询
  if (date) {
    queryType.value = 'custom';
    const selectedDate = dayjs(date).format('YYYY-MM-DD');
    // 查询选中日期当天的数据
    executeQueryByDate(selectedDate, selectedDate);
    
    // 重启数据自动刷新，确保按新的查询条件刷新
    startDataRefresh();
  }
};

// 根据日期执行查询
const executeQueryByDate = async (startDate: string, endDate: string, silent: boolean = false) => {
  try {
    queryLoading.value = true;

    if (!silent) {
      console.log('查询日期范围:', startDate, '至', endDate);
    }

    // 调用API查询数据
    const response = await getPolicePatrolData({
      startTime: startDate + ' 00:00:00',
      endTime: endDate + ' 23:59:59'
    });

    if (response && response.success !== false) {
      const data = response.result || response;
      
      // 更新巡更计划数据
      patrolPlans.value = data.plans || [];
      
      // 如果有民警列表数据，也一并更新
      if (data.police && data.police.length > 0) {
        dashboardData.value.policeList = data.police;
      }
      
      // 如果有统计数据，也一并更新
      if (data.statistics) {
        dashboardData.value.statistics = data.statistics;
      }
      
      if (!silent) {
        console.log(`查询成功，获取到 ${patrolPlans.value.length} 条巡更记录`);
      }
    } else {
      patrolPlans.value = [];
      if (!silent) {
        notification.error({
          message: '查询失败',
          description: response?.message || '查询巡更记录失败，请稍后重试',
          duration: 3
        });
      }
    }
  } catch (error) {
    if (!silent) {
      console.error('查询巡更记录失败:', error);
    }
    patrolPlans.value = [];
    if (!silent) {
      notification.error({
        message: '查询失败',
        description: '网络错误，请检查网络连接后重试',
        duration: 3
      });
    }
  } finally {
    queryLoading.value = false;
  }
};

// 确保日期选择器的z-index
const ensureDatePickerZIndex = (open: boolean) => {
  if (open) {
    // 当日期选择器打开时，确保其z-index足够高
    setTimeout(() => {
      const picker = document.querySelector('.ant-picker-dropdown');
      if (picker) {
        // 在全屏模式下使用更高的z-index
        const zIndex = isFullscreen.value ? '99998' : '9999';
        (picker as HTMLElement).style.zIndex = zIndex;
        (picker as HTMLElement).style.position = 'fixed';
      }
    }, 0);
  }
};



// 测试弹窗
const testModal = () => {
  console.log('测试弹窗被点击');
  console.log('planDetailModalRef.value:', planDetailModalRef.value);
  console.log('当前全屏状态:', isFullscreen.value);

  if (planDetailModalRef.value) {
    console.log('可用方法:', Object.keys(planDetailModalRef.value));

    if (planDetailModalRef.value.testModal) {
      planDetailModalRef.value.testModal();
    } else if (planDetailModalRef.value.showModal) {
      const testPlan = {
        id: 'test-123',
        name: '测试巡更计划',
        routeName: '测试路线',
        startTime: '09:00',
        endTime: '17:00',
        status: 1,
        patrolOfficer: {
          name: '测试民警',
          avatar: ''
        },
        checkpoints: []
      };
      // 添加延迟确保状态同步
      setTimeout(() => {
        planDetailModalRef.value.showModal(testPlan);
      }, 100);
    }
  } else {
    console.error('planDetailModalRef 为空');
  }
};

// 查看计划详情
const viewPlanDetail = (plan: PatrolPlan) => {
  console.log('查看计划详情:', plan);
  console.log('planDetailModalRef.value:', planDetailModalRef.value);
  console.log('当前全屏状态:', isFullscreen.value);

  // 使用PolicePlanDetailModal组件显示详情
  if (planDetailModalRef.value && planDetailModalRef.value.showModal) {
    console.log('准备显示弹窗，全屏状态:', isFullscreen.value);

    try {
      // 确保弹窗组件能获取到最新的全屏状态
      setTimeout(() => {
        planDetailModalRef.value.showModal(plan);
        console.log('showModal 调用成功');
      }, 100);
    } catch (error: any) {
      console.error('调用showModal时出错:', error);
      notification.error({
        message: '功能异常',
        description: '打开详情弹窗时出错: ' + (error?.message || error),
        duration: 3
      });
    }
  } else {
    console.error('PolicePlanDetailModal组件引用不存在或方法未找到');
    console.error('planDetailModalRef.value:', planDetailModalRef.value);
    if (planDetailModalRef.value) {
      console.error('可用方法:', Object.keys(planDetailModalRef.value));
    }
    notification.error({
      message: '功能异常',
      description: '详情弹窗组件加载失败，请刷新页面重试',
      duration: 3
    });
  }
};

// 关闭详情弹窗
const handleDetailModalClose = () => {
  detailModalVisible.value = false;
  selectedPlan.value = null;
};



// 时间更新
let timeInterval: NodeJS.Timeout;
let dataRefreshInterval: NodeJS.Timeout;

const updateTime = () => {
  currentTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
};

// 数据自动刷新
const startDataRefresh = () => {
  // 清除已存在的定时器
  if (dataRefreshInterval) {
    clearInterval(dataRefreshInterval);
  }
  
  // 设置3秒刷新一次数据
  dataRefreshInterval = setInterval(async () => {
    try {
      // 静默刷新：不显示loading，不干扰用户界面
      if (!queryLoading.value && !policeLoading.value) {
        await silentRefreshData();
      }
    } catch (error) {
      // 静默处理错误，不显示给用户
      console.warn('自动刷新数据失败:', error);
    }
  }, 30000); // 30秒刷新一次
};

const stopDataRefresh = () => {
  if (dataRefreshInterval) {
    clearInterval(dataRefreshInterval);
    console.log('停止数据自动刷新');
  }
};

// 静默刷新数据（不显示loading状态）
const silentRefreshData = async () => {
  try {
    const currentQueryType = queryType.value;
    let startDate = '';
    let endDate = '';
    
    if (currentQueryType === 'yesterday') {
      const yesterday = dayjs().subtract(1, 'day');
      startDate = yesterday.format('YYYY-MM-DD');
      endDate = yesterday.format('YYYY-MM-DD');
    } else if (currentQueryType === 'today') {
      const today = dayjs();
      startDate = today.format('YYYY-MM-DD');
      endDate = today.format('YYYY-MM-DD');
    } else if (currentQueryType === 'custom' && customDate.value) {
      const selectedDate = dayjs(customDate.value).format('YYYY-MM-DD');
      startDate = selectedDate;
      endDate = selectedDate;
    } else {
      return; // 无有效查询条件，跳过刷新
    }

    // 直接调用API，不设置loading状态
    const response = await getPolicePatrolData({
      startTime: startDate + ' 00:00:00',
      endTime: endDate + ' 23:59:59'
    });

    if (response && response.success !== false) {
      const data = response.result || response;
      
      // 只有数据真正发生变化时才更新
      const newPlansLength = (data.plans || []).length;
      const currentPlansLength = patrolPlans.value.length;
      
      // 简单的数据变化检测
      const hasDataChanged = newPlansLength !== currentPlansLength || 
                            JSON.stringify(data.plans || []) !== JSON.stringify(patrolPlans.value);
      
      if (hasDataChanged) {
        // 静默更新数据，使用nextTick确保DOM更新的平滑性
        await nextTick(() => {
          // 更新巡更计划数据
          patrolPlans.value = data.plans || [];
          
          // 如果有民警列表数据，也一并更新
          if (data.police && data.police.length > 0) {
            const hasPoliceChanged = JSON.stringify(data.police) !== JSON.stringify(dashboardData.value.policeList);
            if (hasPoliceChanged) {
              dashboardData.value.policeList = data.police;
            }
          }
          
          // 如果有统计数据，也一并更新
          if (data.statistics) {
            dashboardData.value.statistics = data.statistics;
          }
          
          // 更新标题
          if (data.lineName && data.lineName !== dashboardData.value.title.replace('民警巡更数据大屏', '')) {
            dashboardData.value.title = data.lineName + '民警巡更数据大屏';
          }
        });
      }
    }
  } catch (error) {
    // 静默处理错误
    console.warn('静默刷新失败:', error);
  }
};

// 全屏控制
const toggleFullscreen = () => {
  const dashboardElement = document.querySelector('.police-patrol-dashboard') as HTMLElement;
  if (dashboardElement && !document.fullscreenElement) {
    dashboardElement.requestFullscreen().then(() => {
      isFullscreen.value = true;
    }).catch((err) => {
      console.error('无法进入全屏模式:', err);
      // 降级方案：全屏整个页面
      document.documentElement.requestFullscreen().then(() => {
        isFullscreen.value = true;
      }).catch(console.error);
    });
  }
};

const exitFullscreen = () => {
  if (document.fullscreenElement) {
    document.exitFullscreen().then(() => {
      isFullscreen.value = false;
    }).catch(console.error);
  }
};





// 图片错误处理
const handleImageError = (event: Event, police?: any) => {
  const img = event.target as HTMLImageElement;
  img.style.display = 'none';
  if (police) {
    police.imageError = true;
  }
};

// 获取姓名首字母
const getFirstLetter = (name: string) => {
  return name ? name.charAt(0).toUpperCase() : '?';
};

// 根据姓名生成头像背景色
const getAvatarColor = (name: string) => {
  const colors = [
    '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1',
    '#13c2c2', '#eb2f96', '#fa541c', '#a0d911', '#2f54eb'
  ];
  let hash = 0;
  for (let i = 0; i < name.length; i++) {
    hash = name.charCodeAt(i) + ((hash << 5) - hash);
  }
  return colors[Math.abs(hash) % colors.length];
};

// 统一初始化数据加载
const initializeData = async () => {
  try {
    policeLoading.value = true;
    queryLoading.value = true;

    console.log('正在初始化民警巡更数据...');

    // 计算默认查询时间（昨天）
    const yesterday = dayjs().subtract(1, 'day');
    const startDate = yesterday.format('YYYY-MM-DD');
    const endDate = yesterday.format('YYYY-MM-DD');
    
    console.log('初始查询日期范围:', startDate, '至', endDate);

    // 调用真实接口获取民警巡更数据，直接带上时间参数
    const response = await getPolicePatrolData({
      startTime: startDate + ' 00:00:00',
      endTime: endDate + ' 23:59:59'
    });
   
    if (response && response.success !== false) {
      const data = response.result || response;
     
      // 更新大屏数据
      dashboardData.value = {
        title: data.lineName ? data.lineName + '民警巡更数据大屏' : '监区民警巡更数据大屏',
        policeList: data.police || [],
        patrolPlans: data.plans || [],
        statistics: data.statistics || {
          totalPlans: 0,
          completedPlans: 0,
          inProgressPlans: 0,
          missedPlans: 0,
          totalCheckpoints: 0,
          completedCheckpoints: 0,
          missedCheckpoints: 0
        }
      };

      // 同时更新巡更计划数据
      patrolPlans.value = data.plans || [];

      // 设置查询类型为昨天（不触发新的请求）
      queryType.value = 'yesterday';

      console.log(`初始化成功，获取到 ${dashboardData.value.policeList.length} 个民警信息，${patrolPlans.value.length} 条巡更记录`);
    } else {
      console.warn('获取民警数据失败，使用空数据');
      dashboardData.value = {
        title: '监区民警巡更数据大屏',
        policeList: [],
        patrolPlans: [],
        statistics: {
          totalPlans: 0,
          completedPlans: 0,
          inProgressPlans: 0,
          missedPlans: 0,
          totalCheckpoints: 0,
          completedCheckpoints: 0,
          missedCheckpoints: 0
        }
      };
      
      patrolPlans.value = [];
      queryType.value = 'yesterday';
      
      notification.warning({
        message: '数据加载提醒',
        description: response?.message || '暂无民警数据或数据加载失败',
        duration: 3
      });
    }
  } catch (error) {
    console.error('初始化数据失败:', error);
    
    // 设置空数据
    dashboardData.value = {
      title: '监区民警巡更数据大屏',
      policeList: [],
      patrolPlans: [],
      statistics: {
        totalPlans: 0,
        completedPlans: 0,
        inProgressPlans: 0,
        missedPlans: 0,
        totalCheckpoints: 0,
        completedCheckpoints: 0,
        missedCheckpoints: 0
      }
    };
    
    patrolPlans.value = [];
    queryType.value = 'yesterday';
    
    notification.error({
      message: '初始化失败',
      description: '初始化民警信息失败，请检查网络连接或联系管理员',
      duration: 3
    });
  } finally {
    policeLoading.value = false;
    queryLoading.value = false;
  }
};

// 加载数据（保留向后兼容）
const loadData = async () => {
  await initializeData();
};

// 生命周期
onMounted(() => {
  updateTime();
  timeInterval = setInterval(updateTime, 1000);
  
  // 初始化数据（只请求一次，包含时间参数）
  initializeData();

  // 监听全屏变化
  const handleFullscreenChange = () => {
    isFullscreen.value = !!document.fullscreenElement;
  };

  document.addEventListener('fullscreenchange', handleFullscreenChange);
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.addEventListener('mozfullscreenchange', handleFullscreenChange);
  document.addEventListener('MSFullscreenChange', handleFullscreenChange);

  // 启动数据自动刷新
  startDataRefresh();
});

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval);
  }

  // 清理全屏事件监听
  const handleFullscreenChange = () => {
    isFullscreen.value = !!document.fullscreenElement;
  };

  document.removeEventListener('fullscreenchange', handleFullscreenChange);
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
  document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
  document.removeEventListener('MSFullscreenChange', handleFullscreenChange);

  // 停止数据自动刷新
  stopDataRefresh();
});

// 获取巡更点状态文本
const getPointStatusText = (status: number | string) => {
  if (typeof status === 'number' || !isNaN(Number(status))) {
    const numStatus = Number(status);
    switch (numStatus) {
      case 0: return '待巡';
      case 1: return '正常';
      case 2: return '漏检';
      default: return '未知';
    }
  }
  
  // 字符串状态兼容
  const statusMap = {
    'pending': '待巡',
    'completed': '正常',
    'checked': '正常',
    'missed': '漏检'
  };
  
  return statusMap[status.toLowerCase()] || status;
};


</script>

<style lang="less" scoped>
.police-patrol-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg,
    #0a0e27 0%,
    #1a1f3a 25%,
    #2a2f4a 50%,
    #1a1f3a 75%,
    #0a0e27 100%
  );
  color: #ffffff;
  padding: 20px;
  position: relative;
  overflow: hidden;

  // 科技感背景效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, rgba(0, 255, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }

  // 确保内容在背景之上
  > * {
    position: relative;
    z-index: 1;
  }
}

// 标题栏样式
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px 30px;
  background: linear-gradient(135deg,
    rgba(10, 14, 39, 0.8) 0%,
    rgba(26, 31, 58, 0.6) 50%,
    rgba(10, 14, 39, 0.8) 100%
  );
  border: 1px solid rgba(0, 255, 255, 0.3);
  border-radius: 12px;
  backdrop-filter: blur(20px);
  box-shadow:
    0 8px 32px rgba(0, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);

  .header-title {
    .title-glow {
      font-size: clamp(24px, 3vw, 36px);
      font-weight: bold;
      background: linear-gradient(45deg, #00ffff, #ffffff, #00ffff);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
      margin-bottom: 8px;
    }

    .subtitle {
      font-size: clamp(14px, 1.5vw, 18px);
      color: rgba(255, 255, 255, 0.8);
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 20px;

    .header-time {
      font-size: clamp(16px, 1.8vw, 20px);
      font-weight: 500;
      color: #00ffff;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
      font-family: 'Courier New', monospace;
    }

    .fullscreen-btn, .exit-fullscreen-btn {
      height: 40px;
      border-radius: 8px;
      border: 1px solid rgba(0, 255, 255, 0.4);
      background: linear-gradient(135deg, rgba(0, 255, 255, 0.1), rgba(0, 255, 255, 0.2));
      color: #ffffff;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        border-color: rgba(0, 255, 255, 0.6);
        background: linear-gradient(135deg, rgba(0, 255, 255, 0.2), rgba(0, 255, 255, 0.3));
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
        transform: translateY(-2px);
      }
    }
  }
}

// 全局查询区域
.global-query-section {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 1vh 2vw;
  margin-bottom: 1vh;

  .query-controls {
    display: flex;
    align-items: center;
    gap: 12px;

    .date-picker {
      width: 140px;

      :deep(.ant-picker) {
        background: rgba(0, 0, 0, 0.4);
        border: 1px solid rgba(0, 255, 255, 0.4);
        border-radius: 6px;
        color: #ffffff;
        backdrop-filter: blur(10px);
        height: 32px;

        &:hover {
          border-color: rgba(0, 255, 255, 0.6);
        }

        &.ant-picker-focused {
          border-color: rgba(0, 255, 255, 0.8);
          box-shadow: 0 0 10px rgba(0, 255, 255, 0.2);
        }

        .ant-picker-input > input {
          color: #ffffff;
          background: transparent;
          font-size: 12px;

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }
        }

        .ant-picker-suffix {
          color: rgba(0, 255, 255, 0.7);
        }
      }
    }

    .query-btn {
      height: 32px;
      border-radius: 6px;
      border: 1px solid rgba(0, 255, 255, 0.4);
      background: rgba(0, 0, 0, 0.3);
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
      padding: 0 16px;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      min-width: 70px;

      &:hover {
        border-color: rgba(0, 255, 255, 0.6);
        background: rgba(0, 255, 255, 0.1);
        color: #ffffff;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 255, 255, 0.2);
      }

      &.ant-btn-primary {
        background: linear-gradient(135deg, rgba(0, 255, 255, 0.2), rgba(0, 255, 255, 0.3));
        border-color: rgba(0, 255, 255, 0.6);
        color: #00ffff;
        box-shadow: 0 0 10px rgba(0, 255, 255, 0.3);

        &:hover {
          background: linear-gradient(135deg, rgba(0, 255, 255, 0.3), rgba(0, 255, 255, 0.4));
          box-shadow: 0 2px 12px rgba(0, 255, 255, 0.4);
        }
      }

      .anticon {
        margin-right: 4px;
      }
    }
  }
}

// 民警信息区域 - 竖型显示
.police-info-section {
  padding: 2vh 2vw;
  margin-bottom: 15px;

  // 民警数据加载状态
  .loading-police-info {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;

    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 20px;
      color: rgba(255, 255, 255, 0.6);
    }
  }

  // 民警数据为空状态
  .empty-police-info {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;

    // 美观的空状态图标容器
    .empty-icon-container {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 20px;

      .empty-icon-bg {
        position: relative;
        width: 120px;
        height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, 
          rgba(0, 255, 255, 0.1) 0%, 
          rgba(0, 255, 255, 0.05) 50%, 
          rgba(0, 255, 255, 0.1) 100%
        );
        border: 2px solid rgba(0, 255, 255, 0.2);
        border-radius: 50%;
        backdrop-filter: blur(10px);
        
        &::before {
          content: '';
          position: absolute;
          inset: -2px;
          border-radius: 50%;
          background: linear-gradient(45deg, rgba(0, 255, 255, 0.3), transparent, rgba(0, 255, 255, 0.3));
          z-index: -1;
          animation: iconRotate 8s linear infinite;
        }

        .anticon {
          color: rgba(0, 255, 255, 0.7);
          filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.3));
        }
      }

      .empty-decoration {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 140px;
        height: 140px;
        pointer-events: none;

        .decoration-dot {
          position: absolute;
          width: 6px;
          height: 6px;
          background: rgba(0, 255, 255, 0.4);
          border-radius: 50%;
          animation: decorationFloat 3s ease-in-out infinite;

          &:nth-child(1) {
            top: 20px;
            right: 30px;
            animation-delay: 0s;
          }

          &:nth-child(2) {
            bottom: 20px;
            left: 30px;
            animation-delay: 1s;
          }

          &:nth-child(3) {
            top: 50%;
            right: 10px;
            animation-delay: 2s;
          }
        }
      }
    }

    .empty-description {
      text-align: center;

      .empty-title {
        font-size: 18px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 8px;
        text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);
      }

      .empty-subtitle {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.6);
        margin-bottom: 16px;
        line-height: 1.4;
      }

      .empty-tips {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.5);
        margin-bottom: 20px;
        padding: 8px 16px;
        background: rgba(0, 255, 255, 0.05);
        border: 1px solid rgba(0, 255, 255, 0.1);
        border-radius: 20px;
        backdrop-filter: blur(5px);

        .anticon {
          color: rgba(0, 255, 255, 0.6);
        }
      }

      .empty-actions {
        display: flex;
        gap: 12px;
        justify-content: center;

        .ant-btn {
          height: 32px;
          border-radius: 16px;
          font-size: 12px;
          padding: 0 16px;
          border: 1px solid rgba(0, 255, 255, 0.3);
          background: rgba(0, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.8);
          transition: all 0.3s ease;

          &:hover {
            border-color: rgba(0, 255, 255, 0.5);
            background: rgba(0, 255, 255, 0.2);
            color: #ffffff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 255, 255, 0.2);
          }

          &.ant-btn-primary {
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.2), rgba(0, 255, 255, 0.3));
            border-color: rgba(0, 255, 255, 0.5);
            color: #00ffff;

            &:hover {
              background: linear-gradient(135deg, rgba(0, 255, 255, 0.3), rgba(0, 255, 255, 0.4));
              box-shadow: 0 4px 16px rgba(0, 255, 255, 0.4);
            }
          }

          .anticon {
            margin-right: 4px;
          }
        }
      }
    }
  }

  .police-cards {
    display: flex;
    justify-content: center;
    gap: 1vw;
    flex-wrap: wrap;
    max-width: 100%;
  }

  .police-card {
    width: clamp(120px, 10vw, 150px);
    padding: 12px;
    background: rgba(0, 0, 0, 0.4);
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 10px;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    flex-shrink: 0;

    &.on-duty {
      border-color: #00ff88;
      box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
      animation: patrolWalk 4s ease-in-out infinite;

      .status-indicator {
        background: #00ff88;
        box-shadow: 0 0 15px rgba(0, 255, 136, 0.6);
      }
    }

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
    }

    .police-avatar {
      position: relative;
      width: clamp(50px, 5vw, 70px);
      height: clamp(75px, 7.5vw, 105px);
      margin: 0 auto 8px;

      img {
        width: 100%;
        height: 100%;
        border-radius: 8px;
        border: 3px solid rgba(0, 255, 255, 0.5);
        object-fit: cover;
        object-position: center top;
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
      }

      .avatar-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: clamp(32px, 4vw, 48px);
        font-weight: bold;
        color: #ffffff;
        border-radius: 8px;
        text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
      }

      :deep(.anticon) {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        color: rgba(255, 255, 255, 0.6);
        background: rgba(0, 255, 255, 0.1);
        border-radius: 8px;
      }

      .status-indicator {
        position: absolute;
        bottom: -5px;
        right: -5px;
        width: clamp(16px, 2vw, 24px);
        height: clamp(16px, 2vw, 24px);
        border-radius: 50%;
        background: #ff4444;
        border: 2px solid #ffffff;

        &.on-duty {
          background: #00ff88;
          animation: pulse 2s infinite;
        }

        &.offline {
          background: #666666;
        }
      }
    }

    .police-info {
      .police-name {
        font-size: clamp(11px, 1.1vw, 13px);
        font-weight: bold;
        margin-bottom: 4px;
        color: #ffffff;
        line-height: 1.2;
      }

      .police-badge {
        font-size: clamp(9px, 0.9vw, 11px);
        color: #00ffff;
        margin-bottom: 2px;
        font-family: 'Courier New', monospace;
        line-height: 1.2;
      }

      .police-department {
        font-size: clamp(8px, 0.8vw, 10px);
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.2;
      }
    }
  }
}



// 动画效果
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
}

@keyframes statusPulse {
  0%, 100% {
    box-shadow: 0 0 5px rgba(250, 173, 20, 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(250, 173, 20, 0.6);
  }
}

// 巡更点动画
@keyframes currentPointPulse {
  0%, 100% {
    box-shadow: 0 0 15px rgba(24, 144, 255, 0.6);
  }
  50% {
    box-shadow: 0 0 25px rgba(24, 144, 255, 0.9);
  }
}

@keyframes patrolIndicator {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

@keyframes progressRing {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes textGlow {
  0%, 100% {
    text-shadow: 0 0 3px rgba(24, 144, 255, 0.8);
  }
  50% {
    text-shadow: 0 0 8px rgba(24, 144, 255, 1);
  }
}

@keyframes nextPointBlink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes nextTextBlink {
  0%, 100% {
    color: #faad14;
  }
  50% {
    color: #ffc53d;
  }
}

// 全屏模式优化
.police-patrol-dashboard.fullscreen-mode,
.police-patrol-dashboard:fullscreen {
  .dashboard-header {
    .header-title .title-glow {
      font-size: clamp(28px, 3.5vw, 42px);
    }

    .header-title .subtitle {
      font-size: clamp(16px, 1.8vw, 22px);
    }
  }

  // 全屏模式下巡更计划区域优化
  .patrol-plans-section {
    padding-bottom: 4vh; // 增加底部距离，确保与页面底部有足够间距

    .plans-container {
      margin-bottom: 2vh; // 为容器添加底部边距
      max-height: calc(100vh - 54vh); // 限制最大高度，确保不会超出视窗

      .plans-scroll {
        max-height: calc(100vh - 30vh); // 为滚动区域设置合适的最大高度
      }
    }
  }

  .patrol-plans-section .patrol-plans-list {
    max-height: 70vh;
  }
}

// 响应式设计
@media (min-width: 1920px) {
  .police-info-section .police-cards {
    gap: 1.5vw;

    .police-card {
      width: clamp(140px, 10vw, 170px);
      padding: 15px;
    }
  }
}

@media (max-width: 1200px) {
  .police-patrol-dashboard {
    padding: 15px;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .global-query-section {
    padding: 0.8vh 2vw;
    margin-bottom: 0.8vh;
    justify-content: center;

    .query-controls {
      gap: 10px;

      .date-picker {
        width: 120px;

        :deep(.ant-picker) {
          height: 30px;
        }
      }

      .query-btn {
        height: 30px;
        font-size: 11px;
        padding: 0 12px;
        min-width: 60px;
      }
    }
  }

  .police-info-section {
    .police-cards {
      gap: 0.8vw;

      .police-card {
        width: clamp(110px, 10vw, 140px);
        padding: 10px;
      }
    }
  }

  .patrol-plans-section {
    padding-bottom: 3.5vh; // 中等屏幕增加底部距离

    .plans-container {
      margin-bottom: 1.5vh; // 为中等屏幕容器添加底部边距
    }

    .statistics-bar {
      flex-wrap: wrap;
      gap: 15px;
      justify-content: center;
    }

    .patrol-plan-item .plan-header {
      flex-direction: column;
      gap: 15px;
    }

    .checkpoints-section .checkpoints-list {
      grid-template-columns: 1fr;
    }
  }
}

@media (max-width: 768px) {
  .global-query-section {
    padding: 0.5vh 1vw;
    margin-bottom: 0.5vh;
    justify-content: center;

    .query-controls {
      flex-wrap: wrap;
      gap: 6px;
      justify-content: center;

      .date-picker {
        width: 100px;
        order: 3;

        :deep(.ant-picker) {
          height: 28px;
          font-size: 10px;
        }
      }

      .query-btn {
        font-size: 10px;
        height: 28px;
        padding: 0 10px;
        min-width: 50px;
        order: 1;
      }
    }
  }

  .police-info-section {
    padding: 1vh 1vw;

    .police-cards {
      gap: 0.8vw;

      .police-card {
        width: clamp(100px, 11vw, 130px);
        padding: 8px;

        .police-avatar {
          width: clamp(40px, 4vw, 55px);
          height: clamp(60px, 6vw, 82px);
          margin-bottom: 6px;
        }

        .police-info {
          .police-name {
            font-size: clamp(9px, 1vw, 11px);
            margin-bottom: 3px;
          }

          .police-badge {
            font-size: clamp(8px, 0.8vw, 9px);
            margin-bottom: 1px;
          }

          .police-department {
            font-size: clamp(7px, 0.7vw, 8px);
          }
        }
      }
    }
  }

  .patrol-plans-section {
    padding-bottom: 4vh; // 移动端增加更多底部距离

    .plans-container {
      margin-bottom: 2vh; // 为移动端容器添加底部边距
    }

    .statistics-bar {
      .stat-divider {
        display: none;
      }
    }
  }
}

// 巡更计划区域
.patrol-plans-section {
  padding: 0 4vw 3vh; // 增加底部padding，确保与页面底部有适当距离
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;

  .section-title {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 3vh;
    gap: 20px;

    span {
      font-size: clamp(18px, 2.2vw, 28px);
      font-weight: bold;
      color: #00ffff;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
      white-space: nowrap;
    }

    .title-line {
      flex: 1;
      height: 2px;
      background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 255, 255, 0.3) 20%,
        rgba(0, 255, 255, 0.8) 50%,
        rgba(0, 255, 255, 0.3) 80%,
        transparent 100%
      );
      border-radius: 1px;
      box-shadow: 0 0 4px rgba(0, 255, 255, 0.3);
    }
  }



  // 计划容器
  .plans-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    background: rgba(0, 0, 0, 0.2);
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    box-shadow:
      0 8px 32px rgba(0, 255, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    padding: 20px;

    .plans-scroll {
      flex: 1;
      overflow-y: auto;
      padding-right: 10px;

      // 自定义滚动条
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: linear-gradient(to bottom, #00ffff, rgba(0, 255, 255, 0.6));
        border-radius: 3px;

        &:hover {
          background: linear-gradient(to bottom, #00ffff, rgba(0, 255, 255, 0.8));
        }
      }
    }
  }

  // 加载状态
  .loading-patrol-records {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;

    .loading-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 20px;
      color: rgba(255, 255, 255, 0.6);
    }
  }

  // 空数据状态
  .empty-patrol-records {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 300px;

    // 巡更记录空状态专用样式
    .empty-icon-container {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 20px;

      .empty-icon-bg.patrol-empty {
        position: relative;
        width: 100px;
        height: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, 
          rgba(74, 144, 226, 0.1) 0%, 
          rgba(74, 144, 226, 0.05) 50%, 
          rgba(74, 144, 226, 0.1) 100%
        );
        border: 2px solid rgba(74, 144, 226, 0.2);
        border-radius: 20px;
        backdrop-filter: blur(10px);
        
        &::before {
          content: '';
          position: absolute;
          inset: -2px;
          border-radius: 20px;
          background: linear-gradient(45deg, rgba(74, 144, 226, 0.3), transparent, rgba(74, 144, 226, 0.3));
          z-index: -1;
          animation: patrolPulse 4s ease-in-out infinite;
        }

        .anticon {
          color: rgba(74, 144, 226, 0.8);
          filter: drop-shadow(0 0 10px rgba(74, 144, 226, 0.3));
        }
      }

      .empty-decoration.patrol-decoration {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 120px;
        height: 120px;
        pointer-events: none;

        .decoration-ring {
          position: absolute;
          border: 1px solid rgba(74, 144, 226, 0.2);
          border-radius: 50%;
          animation: ringExpand 6s linear infinite;

          &:nth-child(1) {
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            animation-delay: 0s;
          }

          &:nth-child(2) {
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            animation-delay: 3s;
          }
        }
      }
    }

    .empty-description {
      text-align: center;

      .empty-title {
        font-size: 18px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 8px;
        text-shadow: 0 0 10px rgba(74, 144, 226, 0.3);
      }

      .empty-subtitle {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.6);
        margin-bottom: 16px;
        line-height: 1.4;
      }

      .empty-tips {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.5);
        margin-bottom: 20px;
        padding: 8px 16px;
        background: rgba(74, 144, 226, 0.05);
        border: 1px solid rgba(74, 144, 226, 0.1);
        border-radius: 20px;
        backdrop-filter: blur(5px);

        .anticon {
          color: rgba(74, 144, 226, 0.6);
        }
      }

      .empty-actions {
        display: flex;
        gap: 12px;
        justify-content: center;

        .ant-btn {
          height: 32px;
          border-radius: 16px;
          font-size: 12px;
          padding: 0 16px;
          border: 1px solid rgba(74, 144, 226, 0.3);
          background: rgba(74, 144, 226, 0.1);
          color: rgba(255, 255, 255, 0.8);
          transition: all 0.3s ease;

          &:hover {
            border-color: rgba(74, 144, 226, 0.5);
            background: rgba(74, 144, 226, 0.2);
            color: #ffffff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(74, 144, 226, 0.2);
          }

          &.ant-btn-primary {
            background: linear-gradient(135deg, rgba(74, 144, 226, 0.2), rgba(74, 144, 226, 0.3));
            border-color: rgba(74, 144, 226, 0.5);
            color: #4a90e2;

            &:hover {
              background: linear-gradient(135deg, rgba(74, 144, 226, 0.3), rgba(74, 144, 226, 0.4));
              box-shadow: 0 4px 16px rgba(74, 144, 226, 0.3);
            }
          }

          .anticon {
            margin-right: 4px;
          }
        }
      }
    }
  }

  // 计划项
  .plan-item {
    margin-bottom: 15px;
    padding: 20px;
    background: rgba(0, 0, 0, 0.4);
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
      border-color: rgba(0, 255, 255, 0.5);
    }

    &.pending {
      border-color: rgba(255, 193, 7, 0.5);

      &:hover {
        border-color: rgba(255, 193, 7, 0.7);
        box-shadow: 0 10px 30px rgba(255, 193, 7, 0.2);
      }
    }

    &.in-progress {
      border-color: rgba(24, 144, 255, 0.5);
      animation: patrolProgress 3s ease-in-out infinite;

      &:hover {
        border-color: rgba(24, 144, 255, 0.7);
        box-shadow: 0 10px 30px rgba(24, 144, 255, 0.2);
      }
    }

    &.completed {
      border-color: rgba(82, 196, 26, 0.5);

      &:hover {
        border-color: rgba(82, 196, 26, 0.7);
        box-shadow: 0 10px 30px rgba(82, 196, 26, 0.2);
      }
    }

    &.missed {
      border-color: rgba(255, 77, 79, 0.5);

      &:hover {
        border-color: rgba(255, 77, 79, 0.7);
        box-shadow: 0 10px 30px rgba(255, 77, 79, 0.2);
      }
    }

    // 计划基本信息 - 一行显示
    .plan-summary {
      display: flex;
      align-items: center;
      gap: 20px;
      margin-bottom: 20px;
      flex-wrap: wrap;

      .summary-item {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: clamp(12px, 1.2vw, 14px);

        .label {
          color: rgba(255, 255, 255, 0.7);
          font-weight: 500;
          white-space: nowrap;
        }

        .value {
          color: #ffffff;
          font-weight: 600;
        }

        .staff-info {
          display: flex;
          align-items: center;
          gap: 6px;

          .staff-avatar-tiny {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 1px solid rgba(0, 255, 255, 0.5);
            object-fit: cover;
          }
        }
      }

      .plan-status-badge {
        margin-left: auto;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: clamp(11px, 1.1vw, 13px);
        font-weight: 600;
        text-align: center;
        min-width: 60px;

        &.pending {
          background: rgba(255, 193, 7, 0.2);
          color: #ffc107;
          border: 1px solid rgba(255, 193, 7, 0.4);
        }

        &.in-progress {
          background: rgba(24, 144, 255, 0.2);
          color: #1890ff;
          border: 1px solid rgba(24, 144, 255, 0.4);
          animation: statusPulse 3s infinite;
        }

        &.completed {
          background: rgba(82, 196, 26, 0.2);
          color: #52c41a;
          border: 1px solid rgba(82, 196, 26, 0.4);
        }

        &.missed {
          background: rgba(255, 77, 79, 0.2);
          color: #ff4d4f;
          border: 1px solid rgba(255, 77, 79, 0.4);
        }
      }
    }

    // 巡更点分组显示
    .patrol-points-groups {
      margin-top: 15px;
      display: flex;
      flex-direction: row;
      gap: 15px;
      flex-wrap: wrap;
      position: relative;

      // 为进行中的巡更添加扫描线效果
      &.in-progress-scan::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
          transparent 0%,
          rgba(24, 144, 255, 0.15) 50%,
          transparent 100%
        );
        animation: patrolScan 6s linear infinite;
        pointer-events: none;
        z-index: 1;
        border-radius: 12px;
      }

      .point-group {
        background: rgba(0, 0, 0, 0.3);
        border: 2px solid rgba(0, 255, 255, 0.2);
        border-radius: 12px;
        padding: 12px;
        backdrop-filter: blur(5px);
        transition: all 0.3s ease;
        flex: 1;
        min-width: 150px;
        max-width: 200px;
        position: relative;
        z-index: 2;

        &.group-pending {
          border-color: rgba(255, 193, 7, 0.3);
        }

        &.group-in-progress {
          border-color: rgba(24, 144, 255, 0.4);
          box-shadow: 0 0 15px rgba(24, 144, 255, 0.2);
          animation: groupProgress 4s ease-in-out infinite;
        }

        &.group-completed {
          border-color: rgba(82, 196, 26, 0.4);
          box-shadow: 0 0 15px rgba(82, 196, 26, 0.2);
        }

        &.group-has-missed {
          border-color: rgba(255, 77, 79, 0.4);
          box-shadow: 0 0 15px rgba(255, 77, 79, 0.2);
        }

        &.group-partial {
          border-color: rgba(255, 193, 7, 0.4);
          box-shadow: 0 0 15px rgba(255, 193, 7, 0.2);
        }

        .group-header {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-bottom: 10px;
          padding-bottom: 6px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);

          .group-title {
            font-size: clamp(11px, 1.1vw, 14px);
            font-weight: 600;
            color: #00ffff;
            text-shadow: 0 0 5px rgba(0, 255, 255, 0.3);
            text-align: center;
          }
        }

        .group-points {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          align-items: center;
          justify-content: center;

          .patrol-point {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            min-width: 55px;
            max-width: 75px;
            flex: 1;

            // 当前位置特殊样式
            &.is-current {
              .point-icon {
                transform: scale(1.1);
                z-index: 15;
                position: relative;
                border: 3px solid #1890ff !important;
                box-shadow: 0 0 15px rgba(24, 144, 255, 0.6);
              }

              .point-name {
                color: #1890ff;
                font-weight: bold;
                text-shadow: 0 0 3px rgba(24, 144, 255, 0.8);
              }
            }

            // 下一个目标特殊样式
            &.is-next {
              .point-icon {
                border: 2px solid #ffc107 !important;
                box-shadow: 0 0 10px rgba(255, 193, 7, 0.4);
              }

              .point-name {
                color: #ffc107;
                font-weight: 600;
              }
            }

            .point-icon {
              width: clamp(26px, 2.6vw, 36px);
              height: clamp(26px, 2.6vw, 36px);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: clamp(11px, 1.1vw, 16px);
              margin-bottom: 5px;
              transition: all 0.3s ease;
              position: relative;
              z-index: 10;

              &.icon-pending {
                background: rgba(255, 193, 7, 0.2);
                color: #ffc107;
                border: 2px solid rgba(255, 193, 7, 0.5);
              }

              &.icon-checked {
                background: linear-gradient(45deg, #52c41a, #73d13d);
                color: #ffffff;
                border: 2px solid #52c41a;
                box-shadow: 0 0 12px rgba(82, 196, 26, 0.4);
                animation: checkComplete 0.6s ease-out;
              }

              &.icon-current {
                background: linear-gradient(45deg, #1890ff, #40a9ff);
                color: #ffffff;
                border: 2px solid #1890ff;
                box-shadow: 0 0 12px rgba(24, 144, 255, 0.4);
              }

              &.icon-missed {
                background: linear-gradient(45deg, #ff4d4f, #ff7875);
                color: #ffffff;
                border: 2px solid #ff4d4f;
                box-shadow: 0 0 12px rgba(255, 77, 79, 0.4);
              }
            }

            .point-info {
              text-align: center;
              width: 100%;

              .point-name {
                font-size: clamp(9px, 0.9vw, 11px);
                font-weight: 500;
                color: #ffffff;
                margin-bottom: 2px;
                text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 75px;
                text-align: center;
              }

              .point-time {
                font-size: clamp(8px, 0.8vw, 10px);
                color: #52c41a;
                font-family: 'Courier New', monospace;
                text-shadow: 0 0 3px rgba(82, 196, 26, 0.5);
              }
            }
          }
        }
      }
    }

    // 没有巡更点的提示
    .no-checkpoints-tip {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      font-size: clamp(12px, 1.2vw, 14px);
      color: rgba(255, 255, 255, 0.6);
      margin-top: 20px;
      padding: 10px 20px;
      background: rgba(0, 0, 0, 0.3);
      border: 1px dashed rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      backdrop-filter: blur(5px);
      text-align: center;

      .anticon {
        font-size: clamp(18px, 1.8vw, 22px);
        color: rgba(255, 255, 255, 0.4);
      }
    }

    // 没有巡更卡片的提示
    .no-plan-cards-tip {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      font-size: clamp(10px, 1vw, 12px);
      color: rgba(255, 255, 255, 0.5);
      margin: 10px 0;
      padding: 8px 15px;
      background: rgba(0, 0, 0, 0.2);
      border: 1px dashed rgba(255, 255, 255, 0.15);
      border-radius: 6px;
      backdrop-filter: blur(3px);
      text-align: center;

      .anticon {
        font-size: clamp(14px, 1.4vw, 16px);
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }

  // 真实巡检动画效果
  @keyframes patrolProgress {
    0%, 100% {
      box-shadow: 0 0 15px rgba(24, 144, 255, 0.3);
    }
    50% {
      box-shadow: 0 0 25px rgba(24, 144, 255, 0.5);
    }
  }

  @keyframes groupProgress {
    0%, 100% {
      border-color: rgba(24, 144, 255, 0.4);
      box-shadow: 0 0 15px rgba(24, 144, 255, 0.2);
    }
    50% {
      border-color: rgba(24, 144, 255, 0.6);
      box-shadow: 0 0 20px rgba(24, 144, 255, 0.3);
    }
  }

  @keyframes checkComplete {
    0% {
      transform: scale(0.8);
      opacity: 0.8;
    }
    50% {
      transform: scale(1.1);
      opacity: 1;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  @keyframes patrolWalk {
    0%, 100% {
      transform: translateX(0);
    }
    25% {
      transform: translateX(2px);
    }
    75% {
      transform: translateX(-2px);
    }
  }

  @keyframes patrolScan {
    0% {
      left: -100%;
    }
    100% {
      left: 100%;
    }
  }

  @keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  // 空状态动画
  @keyframes iconRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  @keyframes decorationFloat {
    0%, 100% {
      transform: translateY(0) scale(1);
      opacity: 0.4;
    }
    50% {
      transform: translateY(-8px) scale(1.1);
      opacity: 0.8;
    }
  }

  @keyframes patrolPulse {
    0%, 100% {
      transform: scale(1);
      opacity: 0.8;
    }
    50% {
      transform: scale(1.05);
      opacity: 1;
    }
  }

  @keyframes ringExpand {
    0% {
      transform: scale(0.8);
      opacity: 0.3;
    }
    50% {
      transform: scale(1.1);
      opacity: 0.6;
    }
    100% {
      transform: scale(0.8);
      opacity: 0.3;
    }
  }

  @keyframes refreshSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  // 列表过渡动画
  .plan-list-container {
    .plan-list-enter-active, 
    .plan-list-leave-active {
      transition: all 0.3s ease;
    }

    .plan-list-enter-from {
      opacity: 0;
      transform: translateY(10px);
    }

    .plan-list-leave-to {
      opacity: 0;
      transform: translateY(-10px);
    }

    .plan-list-move {
      transition: transform 0.3s ease;
    }
  }

  // 民警卡片过渡动画
  .police-cards {
    .police-list-enter-active, 
    .police-list-leave-active {
      transition: all 0.3s ease;
    }

    .police-list-enter-from {
      opacity: 0;
      transform: scale(0.9) translateY(10px);
    }

    .police-list-leave-to {
      opacity: 0;
      transform: scale(0.9) translateY(-10px);
    }

    .police-list-move {
      transition: transform 0.3s ease;
    }
  }
}

// 全屏模式下的样式调整
.fullscreen-mode {
  .global-query-section {
    padding: 1vh 2vw;
    margin-bottom: 1vh;

    .query-controls {
      gap: 15px;

      .date-picker {
        width: 160px;

        :deep(.ant-picker) {
          height: 36px;
          font-size: 13px;
        }
      }

      .query-btn {
        height: 36px;
        font-size: 13px;
        padding: 0 18px;
        min-width: 80px;
      }
    }
  }

  .police-info-section {
    padding: 1.5vh 1vw;

    .police-cards {
      gap: 1.2vw;

      .police-card {
        width: clamp(130px, 11vw, 160px);
        padding: 10px;
      }
    }
  }

  .patrol-plans-section {
    padding: 0 2vw 1vh;

    .section-title span {
      font-size: clamp(20px, 2.5vw, 32px);
    }

    .plans-scroll {
      max-height: 75vh;
    }
  }

  // 全屏模式下确保弹窗正确显示
  :deep(.ant-modal-root) {
    z-index: 100000 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
  }

  :deep(.ant-modal-mask) {
    z-index: 99999 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: rgba(0, 0, 0, 0.8) !important;
  }

  :deep(.ant-modal-wrap) {
    z-index: 100000 !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  :deep(.ant-modal-content) {
    z-index: 100001 !important;
    position: relative !important;
    transform: none !important;
    margin: 0 !important;
    max-width: 95vw !important;
    max-height: 95vh !important;
    overflow: auto !important;
  }

  :deep(.ant-modal-header) {
    z-index: 100002 !important;
    position: relative !important;
  }

  :deep(.ant-modal-body) {
    z-index: 100002 !important;
    position: relative !important;
    max-height: 85vh !important;
    overflow-y: auto !important;
  }

  :deep(.ant-modal-close) {
    z-index: 100003 !important;
    position: relative !important;
  }

  // 确保j-modal组件在全屏模式下正确显示
  :deep(.j-modal) {
    z-index: 100000 !important;

    .ant-modal-root {
      z-index: 100000 !important;
    }

    .ant-modal-mask {
      z-index: 99999 !important;
    }

    .ant-modal-wrap {
      z-index: 100000 !important;
    }

    .ant-modal-content {
      z-index: 100001 !important;
    }
  }
}
</style>
